class InstallerConfig {
  final String appName;
  final String version;
  final String publisher;
  final String installPath;
  final bool createDesktopShortcut;
  final bool createStartMenuShortcut;
  final bool addToPath;
  final String? appIcon;
  final String? description;
  final String? licenseText;

  InstallerConfig({
    required this.appName,
    required this.version,
    required this.publisher,
    required this.installPath,
    this.createDesktopShortcut = true,
    this.createStartMenuShortcut = true,
    this.addToPath = false,
    this.appIcon,
    this.description,
    this.licenseText,
  });

  String get resolvedExecutablePath => '$installPath\\frontend_re.exe';
  String get uninstallKey => 'SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\$appName';

  InstallerConfig copyWith({
    String? appName,
    String? version,
    String? publisher,
    String? installPath,
    bool? createDesktopShortcut,
    bool? createStartMenuShortcut,
    bool? addToPath,
    String? appIcon,
    String? description,
    String? licenseText,
  }) {
    return InstallerConfig(
      appName: appName ?? this.appName,
      version: version ?? this.version,
      publisher: publisher ?? this.publisher,
      installPath: installPath ?? this.installPath,
      createDesktopShortcut: createDesktopShortcut ?? this.createDesktopShortcut,
      createStartMenuShortcut: createStartMenuShortcut ?? this.createStartMenuShortcut,
      addToPath: addToPath ?? this.addToPath,
      appIcon: appIcon ?? this.appIcon,
      description: description ?? this.description,
      licenseText: licenseText ?? this.licenseText,
    );
  }
}
