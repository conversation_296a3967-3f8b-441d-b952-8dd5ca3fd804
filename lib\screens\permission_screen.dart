import 'package:flutter/material.dart';
import 'dart:io';
import '../services/permission_service.dart';
import 'installer_screen.dart';
import 'package:window_manager/window_manager.dart';
import 'package:flutter_svg/svg.dart';

class PermissionScreen extends StatefulWidget {
  const PermissionScreen({super.key});

  @override
  State<PermissionScreen> createState() => _PermissionScreenState();
}

class _PermissionScreenState extends State<PermissionScreen> {
  bool _isChecking = true;
  bool _hasAdminRights = false;

  @override
  void initState() {
    super.initState();
    _checkPermissions();
  }

  Future<void> _checkPermissions() async {
    // 检查当前是否有管理员权限
    final hasAdmin = PermissionService.isRunningAsAdmin();
    
    setState(() {
      _hasAdminRights = hasAdmin;
      _isChecking = false;
    });

    // 如果已经有管理员权限，直接进入安装器
    if (hasAdmin) {
      _navigateToInstaller();
    }
  }

  Future<void> _requestPermissions() async {
    // 直接触发Windows原生UAC对话框
    // 如果用户点击"是"，应用会以管理员身份重启
    // 如果用户点击"否"，什么都不会发生
    await PermissionService.requestAdminPrivileges();
  }

  void _navigateToInstaller() {
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const InstallerScreen(),
          ),
        );
      }
    });
  }

  void _continueWithoutAdmin() {
    _navigateToInstaller();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // 自定义标题栏
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onPanStart: (details) {
              windowManager.startDragging();
            },
            child: Container(
              height: 50,
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 0),
              decoration: const BoxDecoration(
                color: Color(0xFF1E1C1D),
                boxShadow: [
                  BoxShadow(
                    color: Color(0x1A000000),
                    blurRadius: 4,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  const SizedBox(width: 10),
                  SvgPicture.asset(
                    'assets/logo.svg',
                    width: 20,
                    height: 20,
                  ),
                  const SizedBox(width: 16),
                  const Text(
                    '权限检查',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      height: 1.0,
                    ),
                  ),
                  const Spacer(),
                  // 最小化按钮
                  Tooltip(
                    message: '最小化',
                    child: InkWell(
                      onTap: () async {
                        await windowManager.minimize();
                      },
                      borderRadius: BorderRadius.circular(4),
                      child: Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4),
                          color: Colors.white.withAlpha(25),
                        ),
                        child: const Icon(
                          Icons.remove,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  // 关闭按钮
                  Tooltip(
                    message: '关闭',
                    child: InkWell(
                      onTap: () {
                        exit(0);
                      },
                      borderRadius: BorderRadius.circular(4),
                      child: Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4),
                          color: Colors.red.withAlpha(25),
                        ),
                        child: const Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          // 其余内容
          Expanded(
            child: Builder(
              builder: (context) {
                if (_isChecking) {
                  return _buildCheckingScreen();
                }
                if (_hasAdminRights) {
                  return _buildAdminGrantedScreen();
                }
                return _buildPermissionRequestScreen();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCheckingScreen() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blue.shade50,
            Colors.white,
            Colors.blue.shade50,
          ],
        ),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 24),
            Text(
              '正在检查权限...',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdminGrantedScreen() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.green.shade50,
            Colors.white,
            Colors.green.shade50,
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.green.shade100,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.admin_panel_settings,
                size: 64,
                color: const Color(0xFF0C75F8),
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              '管理员权限已获取',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Color(0xFF0C75F8),
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '正在启动安装向导...',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionRequestScreen() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blue.shade50,
            Colors.white,
            Colors.blue.shade50,
          ],
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 权限图标
            // Container(
            //   padding: const EdgeInsets.all(24),
            //   decoration: BoxDecoration(
            //     color: Colors.orange.shade100,
            //     shape: BoxShape.circle,
            //   ),
            //   child: Icon(
            //     Icons.security,
            //     size: 64,
            //     color: Colors.orange.shade600,
            //   ),
            // ),
            const SizedBox(height: 32),

            // 标题
            Text(
              '需要管理员权限',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: const Color(0xFF0C75F8),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            // 说明
            // Text(
            //   '点击"获取管理员权限"将显示Windows系统的权限提升对话框。\n获取权限后，您可以安装到任何系统目录。',
            //   style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            //     color: Colors.grey.shade600,
            //     height: 1.5,
            //   ),
            //   textAlign: TextAlign.center,
            // ),
            // const SizedBox(height: 32),

            // 权限说明卡片
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Color(0xFF0C75F8),
                        ),
                        SizedBox(width: 8),
                        Text(
                          '获取管理员权限后，您可以：',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF0C75F8),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    _buildPermissionItem(
                      '安装到 C:\\Program Files 目录',
                      Icons.folder,
                    ),
                    _buildPermissionItem(
                      '创建系统级快捷方式',
                      Icons.link,
                    ),
                    _buildPermissionItem(
                      '注册到系统卸载程序',
                      Icons.app_registration,
                    ),
                    _buildPermissionItem(
                      '修改系统环境变量（可选）',
                      Icons.settings,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 32),

            // 操作按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // ElevatedButton.icon(
                //   onPressed: _continueWithoutAdmin,
                //   icon: const Icon(Icons.person),
                //   label: const Text('以普通用户继续'),
                //   style: ElevatedButton.styleFrom(
                //     backgroundColor: Colors.grey.shade100,
                //     foregroundColor: Colors.grey.shade700,
                //     padding: const EdgeInsets.symmetric(
                //       horizontal: 24,
                //       vertical: 12,
                //     ),
                //   ),
                // ),
                // const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: _requestPermissions,
                  icon: const Icon(Icons.admin_panel_settings, color: Colors.white,),
                  label: const Text('获取管理员权限'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF0C75F8),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // 提示信息
            // Container(
            //   padding: const EdgeInsets.all(16),
            //   decoration: BoxDecoration(
            //     color: Colors.blue.shade50,
            //     borderRadius: BorderRadius.circular(8),
            //     border: Border.all(color: Colors.blue.shade200),
            //   ),
            //   child: Row(
            //     children: [
            //       Icon(
            //         Icons.lightbulb_outline,
            //         color: Colors.blue.shade600,
            //         size: 20,
            //       ),
            //       const SizedBox(width: 12),
            //       Expanded(
            //         child: Text(
            //           '提示：如果选择普通用户继续，建议安装到用户目录下',
            //           style: TextStyle(
            //             color: Colors.blue.shade700,
            //             fontSize: 14,
            //           ),
            //         ),
            //       ),
            //     ],
            //   ),
            // ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionItem(String text, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: const Color(0xFF0C75F8),
          ),
          const SizedBox(width: 8),
          Text(
            text,
            style: const TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }
}
