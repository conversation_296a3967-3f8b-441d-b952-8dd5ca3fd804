@echo off
echo ========================================
echo 测试构建修复
echo ========================================
echo.

echo 检查Flutter环境...
flutter --version
echo.

echo 检查项目依赖...
flutter pub get
if %errorlevel% neq 0 (
    echo 错误: 获取依赖失败
    pause
    exit /b 1
)

echo.
echo 分析代码...
flutter analyze
if %errorlevel% neq 0 (
    echo 警告: 代码分析发现问题，但继续构建...
)

echo.
echo 开始构建...
flutter build windows --release --verbose

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo 构建成功！
    echo ========================================
    echo.
    echo 可执行文件: build\windows\runner\Release\installer_app.exe
    echo 文件大小:
    dir "build\windows\runner\Release\installer_app.exe"
    echo.
    
    set /p test_run="是否要测试运行？(y/n): "
    if /i "%test_run%"=="y" (
        echo 启动安装器...
        start "" "build\windows\runner\Release\installer_app.exe"
    )
) else (
    echo.
    echo ========================================
    echo 构建失败！
    echo ========================================
    echo.
    echo 请检查上面的错误信息。
    echo.
    echo 常见解决方案:
    echo 1. 确保Visual Studio已安装C++工作负载
    echo 2. 运行: flutter doctor -v
    echo 3. 清理缓存: flutter clean
    echo 4. 更新Flutter: flutter upgrade
)

echo.
pause
