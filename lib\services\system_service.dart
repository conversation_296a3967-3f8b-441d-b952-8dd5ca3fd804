import 'dart:ffi';
import 'dart:io';
import 'package:ffi/ffi.dart';
import 'package:win32/win32.dart';
import 'package:path/path.dart' as path;
import '../models/installer_config.dart';

class SystemService {
  /// 创建桌面快捷方式
  Future<void> createDesktopShortcut(InstallerConfig config) async {
    if (!Platform.isWindows) return;
    
    try {
      final desktopPath = _getDesktopPath();
      final shortcutPath = path.join(desktopPath, '${config.appName}.lnk');
      
      await _createShortcut(
        shortcutPath: shortcutPath,
        targetPath: config.resolvedExecutablePath,
        workingDirectory: config.installPath,
        description: config.appName,
        iconPath: config.appIcon ?? config.resolvedExecutablePath,
      );
    } catch (e) {
      print('创建桌面快捷方式失败: $e');
    }
  }

  /// 创建开始菜单快捷方式
  Future<void> createStartMenuShortcut(InstallerConfig config) async {
    if (!Platform.isWindows) return;
    
    try {
      final startMenuPath = _getStartMenuPath();
      final programsPath = path.join(startMenuPath, 'Programs', config.publisher);
      
      // 创建发布商文件夹
      await Directory(programsPath).create(recursive: true);
      
      final shortcutPath = path.join(programsPath, '${config.appName}.lnk');
      
      await _createShortcut(
        shortcutPath: shortcutPath,
        targetPath: config.resolvedExecutablePath,
        workingDirectory: config.installPath,
        description: config.appName,
        iconPath: config.appIcon ?? config.resolvedExecutablePath,
      );
      
      // 创建卸载快捷方式
      final uninstallShortcutPath = path.join(programsPath, '卸载 ${config.appName}.lnk');
      await _createShortcut(
        shortcutPath: uninstallShortcutPath,
        targetPath: 'msiexec.exe',
        arguments: '/x ${config.uninstallKey}',
        description: '卸载 ${config.appName}',
      );
    } catch (e) {
      print('创建开始菜单快捷方式失败: $e');
    }
  }

  /// 注册应用程序到Windows注册表
  Future<void> registerApplication(InstallerConfig config) async {
    if (!Platform.isWindows) return;

    try {
      // 使用PowerShell来设置注册表，避免直接使用Win32 API的兼容性问题
      final script = '''
\$regPath = "HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${config.appName}"
New-Item -Path \$regPath -Force | Out-Null
Set-ItemProperty -Path \$regPath -Name "DisplayName" -Value "${config.appName}"
Set-ItemProperty -Path \$regPath -Name "DisplayVersion" -Value "${config.version}"
Set-ItemProperty -Path \$regPath -Name "Publisher" -Value "${config.publisher}"
Set-ItemProperty -Path \$regPath -Name "InstallLocation" -Value "${config.installPath}"
Set-ItemProperty -Path \$regPath -Name "UninstallString" -Value "powershell.exe -Command \\"Remove-Item -Path '${config.installPath}' -Recurse -Force\\""
Set-ItemProperty -Path \$regPath -Name "DisplayIcon" -Value "${config.resolvedExecutablePath}"
Set-ItemProperty -Path \$regPath -Name "InstallDate" -Value "${DateTime.now().toString().substring(0, 10).replaceAll('-', '')}"
Set-ItemProperty -Path \$regPath -Name "EstimatedSize" -Value ${await _getInstallSize(config.installPath) ~/ 1024} -Type DWord
''';

      final result = await Process.run(
        'powershell.exe',
        ['-Command', script],
        runInShell: true,
      );

      if (result.exitCode != 0) {
        print('注册应用程序失败: ${result.stderr}');
      }
    } catch (e) {
      print('注册应用程序失败: $e');
    }
  }

  /// 创建快捷方式
  Future<void> _createShortcut({
    required String shortcutPath,
    required String targetPath,
    String? workingDirectory,
    String? arguments,
    String? description,
    String? iconPath,
  }) async {
    // 使用PowerShell创建快捷方式
    final script = '''
\$WshShell = New-Object -comObject WScript.Shell
\$Shortcut = \$WshShell.CreateShortcut("$shortcutPath")
\$Shortcut.TargetPath = "$targetPath"
${workingDirectory != null ? '\$Shortcut.WorkingDirectory = "$workingDirectory"' : ''}
${arguments != null ? '\$Shortcut.Arguments = "$arguments"' : ''}
${description != null ? '\$Shortcut.Description = "$description"' : ''}
${iconPath != null ? '\$Shortcut.IconLocation = "$iconPath"' : ''}
\$Shortcut.Save()
''';

    final result = await Process.run(
      'powershell.exe',
      ['-Command', script],
      runInShell: true,
    );
    
    if (result.exitCode != 0) {
      throw Exception('PowerShell执行失败: ${result.stderr}');
    }
  }

  /// 获取桌面路径
  String _getDesktopPath() {
    try {
      final buffer = calloc<Utf16>(MAX_PATH);
      final result = SHGetFolderPath(0, CSIDL_DESKTOP, 0, 0, buffer);

      if (result == S_OK) {
        final pathStr = buffer.toDartString();
        calloc.free(buffer);
        return pathStr;
      }

      calloc.free(buffer);
    } catch (e) {
      // 如果 Win32 API 调用失败，使用环境变量
    }

    return path.join(Platform.environment['USERPROFILE'] ?? '', 'Desktop');
  }

  /// 获取开始菜单路径
  String _getStartMenuPath() {
    try {
      final buffer = calloc<Utf16>(MAX_PATH);
      final result = SHGetFolderPath(0, CSIDL_STARTMENU, 0, 0, buffer);

      if (result == S_OK) {
        final pathStr = buffer.toDartString();
        calloc.free(buffer);
        return pathStr;
      }

      calloc.free(buffer);
    } catch (e) {
      // 如果 Win32 API 调用失败，使用环境变量
    }

    return path.join(Platform.environment['APPDATA'] ?? '', 'Microsoft', 'Windows', 'Start Menu');
  }



  /// 获取安装大小
  Future<int> _getInstallSize(String installPath) async {
    int totalSize = 0;
    final directory = Directory(installPath);
    
    if (await directory.exists()) {
      await for (final entity in directory.list(recursive: true)) {
        if (entity is File) {
          try {
            totalSize += await entity.length();
          } catch (e) {
            // 忽略无法访问的文件
          }
        }
      }
    }
    
    return totalSize;
  }

  /// 检查是否以管理员权限运行
  bool isRunningAsAdmin() {
    try {
      final result = Process.runSync(
        'net',
        ['session'],
        runInShell: true,
      );
      return result.exitCode == 0;
    } catch (e) {
      return false;
    }
  }

  /// 请求管理员权限
  Future<bool> requestAdminPrivileges() async {
    if (isRunningAsAdmin()) return true;
    
    try {
      final currentExe = Platform.resolvedExecutable;
      final result = await Process.run(
        'powershell.exe',
        [
          '-Command',
          'Start-Process -FilePath "$currentExe" -Verb RunAs -Wait'
        ],
        runInShell: true,
      );
      
      return result.exitCode == 0;
    } catch (e) {
      return false;
    }
  }
}
