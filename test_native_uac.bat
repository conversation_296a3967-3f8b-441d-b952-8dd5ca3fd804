@echo off
echo ========================================
echo 测试Windows原生UAC权限提升
echo ========================================
echo.

echo 构建应用...
flutter build windows --release

if %errorlevel% neq 0 (
    echo 构建失败！
    pause
    exit /b 1
)

echo.
echo 当前权限状态:
net session >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 当前以管理员身份运行
    echo.
    echo 测试场景: 应用将直接进入安装界面（已有管理员权限）
) else (
    echo ✗ 当前以普通用户身份运行
    echo.
    echo 测试场景: 应用将显示权限选择界面
)

echo.
echo 启动应用测试...
start "" "build\windows\runner\Release\installer_app.exe"

echo.
echo 测试步骤:
echo.
echo 1. 权限检查界面
echo    ✓ 检查是否显示权限选择界面
echo    ✓ 界面说明是否清晰
echo.
echo 2. 点击"获取管理员权限"
echo    ✓ 是否弹出Windows原生UAC对话框
echo    ✓ UAC对话框显示的程序信息是否正确
echo.
echo 3. UAC对话框操作
echo    - 点击"是": 应用会以管理员身份重新启动
echo    - 点击"否": 什么都不会发生，回到权限选择界面
echo.
echo 4. 点击"以普通用户继续"
echo    ✓ 是否直接进入安装界面
echo    ✓ 在路径选择时是否提示权限限制
echo.
echo 5. 管理员权限下的功能测试
echo    ✓ 可以选择 C:\Program Files 目录
echo    ✓ 安装过程是否正常
echo    ✓ 快捷方式创建是否成功
echo    ✓ 注册表注册是否成功
echo.

echo 注意事项:
echo - Windows原生UAC对话框的外观取决于系统主题
echo - 如果程序没有数字签名，UAC会显示"未知发布者"
echo - 管理员权限获取后，当前窗口会关闭并重新打开
echo.

pause
