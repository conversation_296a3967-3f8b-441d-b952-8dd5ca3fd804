import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import 'package:file_selector/file_selector.dart';
import '../providers/installer_provider.dart';

class PathSelectionStep extends StatefulWidget {
  const PathSelectionStep({super.key});

  @override
  State<PathSelectionStep> createState() => _PathSelectionStepState();
}

class _PathSelectionStepState extends State<PathSelectionStep> {
  late TextEditingController _pathController;
  String? _pathError;
  late Future<int> _appFilesSizeFuture;

  @override
  void initState() {
    super.initState();
    final provider = Provider.of<InstallerProvider>(context, listen: false);
    _pathController = TextEditingController(text: provider.config.installPath);
    _appFilesSizeFuture = _getDirectorySize(Directory('C:/Users/<USER>/Desktop/installer_app/app_files'));
  }

  @override
  void dispose() {
    _pathController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<InstallerProvider>(
      builder: (context, provider, child) {
        return Padding(
          padding: const EdgeInsets.all(12.0), // 缩小外边距
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题
                Text(
                  '选择安装位置',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF0C75F8),
                    fontSize: 18,
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  '请选择 ${provider.config.appName} 的安装目录',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey.shade600,
                    fontSize: 13,
                  ),
                ),
                const SizedBox(height: 10),

                // 安装路径选择
                Card(
                  color: Colors.white,
                  elevation: 1,
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '安装目录',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(
                              child: TextField(
                                controller: _pathController,
                                decoration: InputDecoration(
                                  hintText: '选择安装目录...',
                                  border: const OutlineInputBorder(),
                                  errorText: _pathError,
                                  prefixIcon: const Icon(Icons.folder),
                                ),
                                style: const TextStyle(fontSize: 13),
                                onChanged: (value) {
                                  setState(() {
                                    _pathError = _validatePath(value);
                                  });
                                  if (_pathError == null) {
                                    final provider = Provider.of<InstallerProvider>(context, listen: false);
                                    provider.updateInstallPath(value);
                                  }
                                },
                              ),
                            ),
                            const SizedBox(width: 8),
                            ElevatedButton.icon(
                              onPressed: _selectPath,
                              icon: const Icon(Icons.folder_open, size: 18),
                              label: const Text('浏览', style: TextStyle(fontSize: 13)),
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 10,
                                  vertical: 8,
                                ),
                                minimumSize: const Size(0, 32),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        // 融合磁盘空间和所需空间
                        FutureBuilder<int>(
                          future: _appFilesSizeFuture,
                          builder: (context, snapshot) {
                            String sizeText = '...';
                            if (snapshot.hasData) {
                              final mb = (snapshot.data! / (1024 * 1024)).toStringAsFixed(1);
                              sizeText = '$mb MB';
                            }
                            return _buildSpaceInfo(extraInfo: '所需空间：$sizeText');
                          },
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 10),

                // 极简快捷方式选项
                Row(
                  children: [
                    Expanded(
                      child: CheckboxListTile(
                        contentPadding: EdgeInsets.zero,
                        dense: true,
                        title: const Text('创建桌面快捷方式', style: TextStyle(fontSize: 13)),
                        value: provider.config.createDesktopShortcut,
                        onChanged: (value) {
                          provider.updateShortcutSettings(desktop: value);
                        },
                        controlAffinity: ListTileControlAffinity.leading,
                      ),
                    ),
                    Expanded(
                      child: CheckboxListTile(
                        contentPadding: EdgeInsets.zero,
                        dense: true,
                        title: const Text('创建开始菜单快捷方式', style: TextStyle(fontSize: 13)),
                        value: provider.config.createStartMenuShortcut,
                        onChanged: (value) {
                          provider.updateShortcutSettings(startMenu: value);
                        },
                        controlAffinity: ListTileControlAffinity.leading,
                      ),
                    ),
                  ],
                ),
                // const SizedBox(height: 10),

                // 极简安装信息摘要
                // Container(
                //   padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                //   decoration: BoxDecoration(
                //     color: Colors.blue.shade50,
                //     borderRadius: BorderRadius.circular(8),
                //     border: Border.all(color: Colors.blue.shade100),
                //   ),
                //   child: FutureBuilder<int>(
                //     future: _appFilesSizeFuture,
                //     builder: (context, snapshot) {
                //       String sizeText = '...';
                //       if (snapshot.hasData) {
                //         final mb = (snapshot.data! / (1024 * 1024)).toStringAsFixed(1);
                //         sizeText = '$mb MB';
                //       }
                //       return Row(
                //         children: [
                //           const Icon(Icons.storage, size: 16, color: Color(0xFF0C75F8)),
                //           const SizedBox(width: 6),
                //           Text(
                //             '所需空间：',
                //             style: Theme.of(context).textTheme.bodySmall?.copyWith(
                //               fontWeight: FontWeight.w500,
                //               color: Colors.blue.shade700,
                //               fontSize: 13,
                //             ),
                //           ),
                //           const SizedBox(width: 4),
                //           Text(
                //             sizeText,
                //             style: Theme.of(context).textTheme.bodySmall?.copyWith(
                //               color: Colors.grey.shade700,
                //               fontSize: 13,
                //             ),
                //           ),
                //         ],
                //       );
                //     },
                //   ),
                // ),
                // const SizedBox(height: 6),
              ],
            ),
          ),
        );
      },
    );
  }


  Widget _buildSpaceInfo({String? extraInfo}) {
    return FutureBuilder<Map<String, int>>(
      future: _getDiskSpace(_pathController.text),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          final data = snapshot.data!;
          final freeSpace = data['free'] ?? 0;
          final totalSpace = data['total'] ?? 0;
          final usedSpace = totalSpace - freeSpace;
          final freeSpaceMB = (freeSpace / (1024 * 1024)).round();
          final totalSpaceMB = (totalSpace / (1024 * 1024)).round();

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '磁盘空间',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: totalSpace > 0 ? usedSpace / totalSpace : 0,
                backgroundColor: Colors.grey.shade300,
                valueColor: AlwaysStoppedAnimation<Color>(
                  freeSpaceMB > 100 ? Colors.green : Colors.orange,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Text(
                    '可用空间: $freeSpaceMB MB / $totalSpaceMB MB',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                  if (extraInfo != null) ...[
                    const SizedBox(width: 12),
                    const Icon(Icons.storage, size: 15, color: Color(0xFF0C75F8)),
                    const SizedBox(width: 2),
                    Text(
                      extraInfo,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: const Color(0xFF0C75F8),
                        fontSize: 12,
                      ),
                    ),
                  ]
                ],
              ),
            ],
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Future<void> _selectPath() async {
    if (!mounted) return;

    try {
      final String? selectedDirectory = await getDirectoryPath(
        confirmButtonText: '选择',
        initialDirectory: _pathController.text.isNotEmpty ? _pathController.text : null,
      );
      if (selectedDirectory != null && selectedDirectory.isNotEmpty) {
        setState(() {
          _pathController.text = selectedDirectory;
          _pathError = _validatePath(selectedDirectory);
        });
        if (_pathError == null) {
          final provider = Provider.of<InstallerProvider>(context, listen: false);
          provider.updateInstallPath(selectedDirectory);
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('无法打开文件夹选择对话框，请手动输入路径'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  String? _validatePath(String path) {
    if (path.isEmpty) {
      return '请选择安装目录';
    }

    // 检查路径是否有效
    try {
      final directory = Directory(path);
      final parent = directory.parent;
      if (!parent.existsSync()) {
        return '父目录不存在';
      }
    } catch (e) {
      return '无效的路径';
    }

    // 检查是否有写入权限（简单检查）
    if (path.startsWith('C:\\Windows') || path.startsWith('C:\\Program Files')) {
      // 这些目录通常需要管理员权限
    }

    return null;
  }

  Future<Map<String, int>> _getDiskSpace(String path) async {
    try {
      if (path.isEmpty) return {'free': 0, 'total': 0};
      
      // 检查路径是否存在，如果不存在则检查父目录
      
      if (Platform.isWindows) {
        // 在Windows上获取磁盘空间信息
        final result = await Process.run(
          'wmic',
          ['logicaldisk', 'where', 'caption="${path.substring(0, 2)}"', 'get', 'size,freespace', '/format:csv'],
          runInShell: true,
        );
        
        if (result.exitCode == 0) {
          final lines = result.stdout.toString().split('\n');
          for (final line in lines) {
            if (line.contains(',') && !line.startsWith('Node')) {
              final parts = line.split(',');
              if (parts.length >= 3) {
                final freeSpace = int.tryParse(parts[1]) ?? 0;
                final totalSpace = int.tryParse(parts[2]) ?? 0;
                return {'free': freeSpace, 'total': totalSpace};
              }
            }
          }
        }
      }
      
      return {'free': 0, 'total': 0};
    } catch (e) {
      return {'free': 0, 'total': 0};
    }
  }

  Future<int> _getDirectorySize(Directory dir) async {
    int size = 0;
    try {
      if (await dir.exists()) {
        await for (var entity in dir.list(recursive: true, followLinks: false)) {
          if (entity is File) {
            size += await entity.length();
          }
        }
      }
    } catch (e) {
      // ignore
    }
    return size;
  }

}
