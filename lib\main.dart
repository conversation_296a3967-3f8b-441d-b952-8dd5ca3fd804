import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:window_manager/window_manager.dart';
import 'dart:io';
import 'screens/installer_screen.dart';
import 'screens/permission_screen.dart';
import 'providers/installer_provider.dart';
import 'config/window_config.dart';
import 'services/permission_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 在Windows上设置窗口属性
  if (Platform.isWindows) {
    await _setupWindowProperties();
  }

  runApp(const InstallerApp());
}

/// 设置Windows窗口属性
Future<void> _setupWindowProperties() async {
  try {
    // 确保窗口管理器已初始化
    await windowManager.ensureInitialized();

    // 设置窗口选项
    const windowOptions = WindowOptions(
      size: WindowConfig.windowSize,
      minimumSize: WindowConfig.minWindowSize,
      maximumSize: WindowConfig.maxWindowSize,
      center: WindowConfig.centerWindow,
      backgroundColor: WindowConfig.backgroundColor,
      skipTaskbar: WindowConfig.skipTaskbar,
      titleBarStyle: TitleBarStyle.hidden, // 隐藏标题栏
      windowButtonVisibility: WindowConfig.windowButtonVisibility,
    );

    // 等待窗口创建并设置属性
    await windowManager.waitUntilReadyToShow(windowOptions, () async {
      await windowManager.show();
      await windowManager.focus();
      await windowManager.setResizable(false);
      await windowManager.setMaximizable(false);
      await windowManager.setTitle(WindowConfig.windowTitle);
    });
  } catch (e) {
    // 忽略设置窗口属性的错误，继续运行应用
    debugPrint('设置窗口属性失败: $e');
  }
}

class InstallerApp extends StatelessWidget {
  const InstallerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => InstallerProvider(),
      child: MaterialApp(
        title: '应用安装器',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: const Color(0xFF2196F3),
            brightness: Brightness.light,
          ),
          useMaterial3: true,
        ),
        home: const PermissionScreen(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
