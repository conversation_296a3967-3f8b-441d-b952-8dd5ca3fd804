# 构建问题修复说明

## 问题描述

在构建Flutter Windows安装器时遇到了Win32包API兼容性问题，主要错误包括：

1. `REG_OPTION_NON_VOLATILE` 未定义
2. `SHGFP_TYPE_CURRENT` 未定义  
3. `Pointer<Uint16>` 无法转换为 `Pointer<Utf16>`
4. `toDartString()` 方法未定义
5. `REG_SZ` 和 `REG_DWORD` 已弃用

## 解决方案

### 1. 创建简化的系统服务

我创建了 `SimpleSystemService` 类来替代原来的 `SystemService`，主要改进：

- 使用PowerShell脚本代替直接的Win32 API调用
- 避免了复杂的FFI指针操作
- 提供相同的功能但更稳定

### 2. 修复的具体问题

**注册表操作**：
```dart
// 原来的方式（有兼容性问题）
RegCreateKeyEx(HKEY_LOCAL_MACHINE, keyPtr, 0, nullptr, REG_OPTION_NON_VOLATILE, ...);

// 新的方式（使用PowerShell）
final script = '''
\$regPath = "HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${config.appName}"
New-Item -Path \$regPath -Force | Out-Null
Set-ItemProperty -Path \$regPath -Name "DisplayName" -Value "${config.appName}"
''';
```

**路径获取**：
```dart
// 原来的方式（有兼容性问题）
final buffer = calloc<Uint16>(MAX_PATH);
final result = SHGetFolderPath(0, CSIDL_DESKTOP, 0, SHGFP_TYPE_CURRENT, buffer);

// 新的方式（使用环境变量）
String _getDesktopPath() {
  return path.join(Platform.environment['USERPROFILE'] ?? '', 'Desktop');
}
```

### 3. 文件结构更新

- `lib/services/simple_system_service.dart` - 新的简化系统服务
- `lib/services/system_service.dart` - 保留原版本（可能有兼容性问题）
- `lib/providers/installer_provider.dart` - 更新为使用简化服务

## 构建步骤

### 方法1：使用快速构建脚本

```bash
# 运行快速构建脚本
quick_build.bat
```

### 方法2：手动构建

```bash
# 1. 清理缓存
flutter clean

# 2. 获取依赖
flutter pub get

# 3. 构建Windows应用
flutter build windows --release
```

### 方法3：如果仍有问题

如果仍然遇到Win32相关的构建错误，可以尝试：

1. **更新Flutter和Dart SDK**：
   ```bash
   flutter upgrade
   ```

2. **更新依赖包**：
   ```bash
   flutter pub upgrade
   ```

3. **检查Win32包版本**：
   在 `pubspec.yaml` 中确保使用兼容版本：
   ```yaml
   dependencies:
     win32: ^5.2.0  # 或更新版本
     ffi: ^2.1.0
   ```

4. **使用纯Dart实现**：
   如果Win32包仍有问题，可以完全移除Win32依赖，只使用PowerShell和Process.run()来实现系统集成功能。

## 功能验证

构建成功后，安装器应该具备以下功能：

- ✅ 欢迎页面显示
- ✅ 许可协议页面
- ✅ 路径选择和配置
- ✅ 安装进度显示
- ✅ 文件复制功能
- ✅ 快捷方式创建（通过PowerShell）
- ✅ 注册表注册（通过PowerShell）
- ✅ 安装完成页面

## 测试建议

1. **基本功能测试**：
   - 运行安装器
   - 完成整个安装流程
   - 检查文件是否正确复制
   - 验证快捷方式是否创建

2. **权限测试**：
   - 以普通用户身份运行
   - 以管理员身份运行
   - 测试权限提升功能

3. **错误处理测试**：
   - 磁盘空间不足
   - 权限不够
   - 目标目录已存在

## 故障排除

### 如果构建仍然失败

1. **检查Flutter环境**：
   ```bash
   flutter doctor -v
   ```

2. **检查Visual Studio组件**：
   确保安装了"使用C++的桌面开发"工作负载

3. **清理并重建**：
   ```bash
   flutter clean
   rm -rf build/
   flutter pub get
   flutter build windows --release
   ```

4. **查看详细错误**：
   ```bash
   flutter build windows --release --verbose
   ```

### 如果运行时出错

1. **检查依赖**：
   确保目标机器安装了Visual C++ Redistributable

2. **检查权限**：
   某些操作需要管理员权限

3. **检查路径**：
   确保安装路径有效且可写

## 联系支持

如果仍然遇到问题，请提供：
- 完整的错误日志
- Flutter版本信息 (`flutter --version`)
- 操作系统版本
- Visual Studio版本信息

这样我可以提供更具体的解决方案。
