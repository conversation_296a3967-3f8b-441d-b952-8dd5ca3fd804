@echo off
echo ========================================
echo 测试窗口大小功能
echo ========================================
echo.

echo 构建应用...
flutter build windows --release

if %errorlevel% neq 0 (
    echo 构建失败！
    pause
    exit /b 1
)

echo.
echo 启动应用并测试窗口功能...
echo.
echo 测试项目:
echo 1. 窗口大小固定为 900x700
echo 2. 窗口不可调整大小
echo 3. 窗口不可最大化
echo 4. 窗口居中显示
echo 5. 自定义窗口控制按钮
echo.

start "" "build\windows\runner\Release\installer_app.exe"

echo 应用已启动，请检查以下功能:
echo.
echo ✓ 窗口大小是否为 900x700 像素
echo ✓ 尝试拖拽窗口边缘，应该无法调整大小
echo ✓ 点击最大化按钮，应该无效果
echo ✓ 窗口是否在屏幕中央
echo ✓ 标题栏右侧是否有自定义的最小化和关闭按钮
echo ✓ 点击最小化按钮是否正常工作
echo ✓ 点击关闭按钮是否显示确认对话框
echo.

pause
