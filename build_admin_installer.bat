@echo off
echo ========================================
echo 构建管理员权限安装器
echo ========================================
echo.

echo 这个脚本会构建两个版本的安装器:
echo 1. 普通版本 - 启动时可选择权限提升
echo 2. 管理员版本 - 启动时自动请求管理员权限
echo.

echo 1. 清理构建缓存...
flutter clean

echo 2. 获取依赖包...
flutter pub get

echo 3. 构建普通版本...
flutter build windows --release
if %errorlevel% neq 0 (
    echo 构建失败！
    pause
    exit /b 1
)

echo 4. 创建分发目录...
if not exist "dist" mkdir dist
if exist "dist\normal" rmdir /s /q "dist\normal"
if exist "dist\admin" rmdir /s /q "dist\admin"
mkdir "dist\normal"
mkdir "dist\admin"

echo 5. 复制普通版本...
xcopy "build\windows\runner\Release\*" "dist\normal\" /E /I /Y

echo 6. 准备管理员版本...
echo 正在应用管理员权限清单文件...

REM 检查是否存在清单文件
if exist "windows\runner\app.manifest" (
    echo ✓ 找到管理员权限清单文件
    
    REM 这里需要使用资源编辑器将清单文件嵌入到exe中
    REM 由于这需要额外的工具，我们提供一个简化的方案
    
    echo 复制文件到管理员版本目录...
    xcopy "build\windows\runner\Release\*" "dist\admin\" /E /I /Y
    
    echo 创建管理员启动脚本...
    echo @echo off > "dist\admin\run_as_admin.bat"
    echo echo 正在以管理员身份启动安装器... >> "dist\admin\run_as_admin.bat"
    echo powershell.exe -Command "Start-Process -FilePath '%~dp0installer_app.exe' -Verb RunAs" >> "dist\admin\run_as_admin.bat"
    
    echo 创建说明文件...
    echo 管理员权限安装器 > "dist\admin\README.txt"
    echo =================== >> "dist\admin\README.txt"
    echo. >> "dist\admin\README.txt"
    echo 使用方法: >> "dist\admin\README.txt"
    echo 1. 双击 run_as_admin.bat 以管理员身份运行 >> "dist\admin\README.txt"
    echo 2. 或者右键点击 installer_app.exe，选择"以管理员身份运行" >> "dist\admin\README.txt"
    echo. >> "dist\admin\README.txt"
    echo 管理员权限允许安装到: >> "dist\admin\README.txt"
    echo - C:\Program Files >> "dist\admin\README.txt"
    echo - C:\Program Files (x86) >> "dist\admin\README.txt"
    echo - 系统目录 >> "dist\admin\README.txt"
    
) else (
    echo ✗ 未找到管理员权限清单文件
    echo 将创建普通版本的副本...
    xcopy "build\windows\runner\Release\*" "dist\admin\" /E /I /Y
)

echo 7. 复制应用文件...
if exist "app_files" (
    xcopy "app_files\*" "dist\normal\app_files\" /E /I /Y
    xcopy "app_files\*" "dist\admin\app_files\" /E /I /Y
)

echo 8. 复制资源文件...
if exist "assets" (
    xcopy "assets\*" "dist\normal\assets\" /E /I /Y
    xcopy "assets\*" "dist\admin\assets\" /E /I /Y
)

echo 9. 创建版本信息...
echo [Normal Version] > "dist\normal\version_info.txt"
echo Version=1.0.0 >> "dist\normal\version_info.txt"
echo Type=Normal >> "dist\normal\version_info.txt"
echo Description=普通版本，启动时可选择权限提升 >> "dist\normal\version_info.txt"
echo BuildDate=%date% %time% >> "dist\normal\version_info.txt"

echo [Admin Version] > "dist\admin\version_info.txt"
echo Version=1.0.0 >> "dist\admin\version_info.txt"
echo Type=Admin >> "dist\admin\version_info.txt"
echo Description=管理员版本，启动时自动请求管理员权限 >> "dist\admin\version_info.txt"
echo BuildDate=%date% %time% >> "dist\admin\version_info.txt"

echo.
echo ========================================
echo 构建完成！
echo ========================================
echo.
echo 普通版本: dist\normal\installer_app.exe
echo - 启动时显示权限选择界面
echo - 用户可以选择以普通用户或管理员身份运行
echo.
echo 管理员版本: dist\admin\
echo - 使用 run_as_admin.bat 启动
echo - 或右键选择"以管理员身份运行"
echo - 自动获取管理员权限
echo.

set /p open_dist="是否要打开分发目录？(y/n): "
if /i "%open_dist%"=="y" (
    explorer "dist"
)

echo.
echo 使用建议:
echo - 对于普通用户，推荐使用普通版本
echo - 对于企业部署，推荐使用管理员版本
echo - 两个版本功能完全相同，只是权限获取方式不同
echo.

pause
