import 'dart:io';

class PermissionService {
  /// 检查是否以管理员身份运行
  static bool isRunningAsAdmin() {
    try {
      final result = Process.runSync(
        'net',
        ['session'],
        runInShell: true,
      );
      // 记录权限检测到日志
      final logFile = File('installer_log.txt');
      logFile.writeAsStringSync('[${DateTime.now()}] isRunningAsAdmin: exitCode=${result.exitCode}\n', mode: FileMode.append);
      logFile.writeAsStringSync('[${DateTime.now()}] USERPROFILE: \'${Platform.environment['USERPROFILE']}\'\n', mode: FileMode.append);
      logFile.writeAsStringSync('[${DateTime.now()}] APPDATA: \'${Platform.environment['APPDATA']}\'\n', mode: FileMode.append);
      logFile.writeAsStringSync('[${DateTime.now()}] USERNAME: \'${Platform.environment['USERNAME']}\'\n', mode: FileMode.append);
      logFile.writeAsStringSync('-----------------------------\n', mode: FileMode.append);
      return result.exitCode == 0;
    } catch (e) {
      final logFile = File('installer_log.txt');
      logFile.writeAsStringSync('[${DateTime.now()}] isRunningAsAdmin Exception: $e\n', mode: FileMode.append);
      return false;
    }
  }

  /// 请求管理员权限（重启应用）- 会触发Windows原生UAC对话框
  static Future<bool> requestAdminPrivileges() async {
    if (isRunningAsAdmin()) return true;

    try {
      final currentExe = Platform.resolvedExecutable;

      // 使用PowerShell的Start-Process -Verb RunAs会触发Windows原生UAC对话框
      final result = await Process.run(
        'powershell.exe',
        ['-Command', 'Start-Process -FilePath "$currentExe" -Verb RunAs'],
        runInShell: true,
      );

      if (result.exitCode == 0) {
        // UAC对话框被用户确认，新的管理员进程已启动，退出当前进程
        exit(0);
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  /// 检查路径是否需要管理员权限
  static bool pathRequiresAdmin(String path) {
    final lowerPath = path.toLowerCase();
    return lowerPath.contains('program files') ||
           lowerPath.contains('windows') ||
           lowerPath.startsWith('c:\\') && !lowerPath.contains('users');
  }

  /// 测试路径写权限
  static Future<bool> testWritePermission(String path) async {
    try {
      final testDir = Directory(path);
      final testFile = File('$path\\permission_test.tmp');

      // 尝试创建目录和文件
      await testDir.create(recursive: true);
      await testFile.writeAsString('test');
      await testFile.delete();

      return true;
    } catch (e) {
      return false;
    }
  }
}
