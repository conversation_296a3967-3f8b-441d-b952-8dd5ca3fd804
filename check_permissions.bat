@echo off
echo ========================================
echo 权限检查工具
echo ========================================
echo.

echo 检查当前用户权限...
net session >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 当前以管理员身份运行
    echo.
    echo 可以安装到以下位置:
    echo - C:\Program Files\应用名
    echo - C:\Program Files (x86)\应用名
    echo - 任何系统目录
) else (
    echo ✗ 当前以普通用户身份运行
    echo.
    echo 建议安装到以下位置:
    echo - %USERPROFILE%\AppData\Local\应用名
    echo - %USERPROFILE%\Documents\应用名
    echo - D:\应用名 (如果D盘存在且可写)
    echo.
    echo 如需安装到 Program Files，请:
    echo 1. 右键点击安装器
    echo 2. 选择"以管理员身份运行"
)

echo.
echo 检查常用安装目录的写权限...
echo.

echo 测试 Program Files 目录...
mkdir "C:\Program Files\PermissionTest" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ C:\Program Files - 可写
    rmdir "C:\Program Files\PermissionTest" >nul 2>&1
) else (
    echo ✗ C:\Program Files - 需要管理员权限
)

echo 测试用户 AppData 目录...
mkdir "%USERPROFILE%\AppData\Local\PermissionTest" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ %USERPROFILE%\AppData\Local - 可写
    rmdir "%USERPROFILE%\AppData\Local\PermissionTest" >nul 2>&1
) else (
    echo ✗ %USERPROFILE%\AppData\Local - 权限问题
)

echo 测试用户文档目录...
mkdir "%USERPROFILE%\Documents\PermissionTest" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ %USERPROFILE%\Documents - 可写
    rmdir "%USERPROFILE%\Documents\PermissionTest" >nul 2>&1
) else (
    echo ✗ %USERPROFILE%\Documents - 权限问题
)

echo.
echo ========================================
echo 建议
echo ========================================
echo.

if %errorlevel% equ 0 (
    echo 推荐安装路径（按优先级排序）:
    echo 1. %USERPROFILE%\AppData\Local\应用名 （推荐）
    echo 2. %USERPROFILE%\Documents\应用名
    echo 3. C:\Program Files\应用名 （需要管理员权限）
) else (
    echo 如果遇到权限问题:
    echo 1. 选择用户目录进行安装
    echo 2. 或以管理员身份运行安装器
    echo 3. 检查杀毒软件是否阻止了文件操作
)

echo.
pause
