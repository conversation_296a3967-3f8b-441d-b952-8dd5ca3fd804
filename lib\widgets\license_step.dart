import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/installer_provider.dart';

class LicenseStep extends StatefulWidget {
  const LicenseStep({super.key});

  @override
  State<LicenseStep> createState() => _LicenseStepState();
}

class _LicenseStepState extends State<LicenseStep> {
  bool _acceptLicense = false;

  @override
  Widget build(BuildContext context) {
    return Consumer<InstallerProvider>(
      builder: (context, provider, child) {
        return Padding(
          padding: const EdgeInsets.only(left: 32, right: 32, top: 32, bottom: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Text(
                '许可协议',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
              ),
              
              const SizedBox(height: 8),
              
              Text(
                '请仔细阅读以下许可协议。您必须接受此协议的条款才能安装此应用程序。',
                style: TextStyle(
                  color: Colors.grey.shade600,
                ),
              ),
              
              const SizedBox(height: 8),
              
              // 许可协议内容
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    // border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: SingleChildScrollView(
                    child: Text(
                      provider.config.licenseText ?? _getDefaultLicenseText(),
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 12,
                        height: 1.4,
                      ),
                    ),
                  ),
                ),
              ),
              
              const SizedBox(height: 8),
              
              // 接受协议复选框
              Container(
                height: 40,
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Row(
                  children: [
                    Checkbox(
                      value: provider.licenseAccepted,
                      onChanged: (value) {
                        provider.licenseAccepted = value ?? false;
                      },
                      activeColor: Colors.blue.shade600,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        '我已阅读并同意上述许可协议的条款和条件',
                        style: TextStyle(
                          color: Colors.grey.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              // const SizedBox(height: 16),
              
              // 提示信息
              // if (!_acceptLicense)
              //   Container(
              //     padding: const EdgeInsets.all(12),
              //     decoration: BoxDecoration(
              //       color: Colors.orange.shade50,
              //       borderRadius: BorderRadius.circular(8),
              //       border: Border.all(color: Colors.orange.shade200),
              //     ),
              //     child: Row(
              //       children: [
              //         Icon(
              //           Icons.info_outline,
              //           color: Colors.orange.shade600,
              //           size: 20,
              //         ),
              //         const SizedBox(width: 8),
              //         Expanded(
              //           child: Text(
              //             '您必须接受许可协议才能继续安装',
              //             style: TextStyle(
              //               color: Colors.orange.shade700,
              //               fontSize: 13,
              //             ),
              //           ),
              //         ),
              //       ],
              //     ),
              //   ),
            ],
          ),
        );
      },
    );
  }

  String _getDefaultLicenseText() {
    return '''
软件许可协议

版权所有 (c) 2025 我的公司

特此免费授予任何获得本软件副本和相关文档文件（"软件"）的人不受限制地处理软件的权利，包括但不限于使用、复制、修改、合并、发布、分发、再许可和/或销售软件副本的权利，并允许向其提供软件的人员这样做，但须符合以下条件：

上述版权声明和本许可声明应包含在软件的所有副本或重要部分中。

本软件按"原样"提供，不提供任何形式的明示或暗示保证，包括但不限于对适销性、特定用途适用性和非侵权性的保证。在任何情况下，作者或版权持有人均不对任何索赔、损害或其他责任负责，无论是在合同诉讼、侵权行为还是其他方面，由软件或软件的使用或其他交易引起、由软件引起或与软件相关。

使用条款：
1. 您可以在任何数量的设备上安装和使用本软件
2. 您不得逆向工程、反编译或反汇编本软件
3. 您不得删除或修改任何版权声明
4. 本许可协议受中华人民共和国法律管辖

如果您不同意这些条款，请不要安装或使用本软件。

联系信息：
公司：我的公司
邮箱：<EMAIL>
网站：https://www.mycompany.com

最后更新：2025年1月
''';
  }
}
