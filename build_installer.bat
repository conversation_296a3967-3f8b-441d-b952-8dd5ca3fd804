@echo off
echo ========================================
echo Flutter Windows 安装器构建脚本
echo ========================================
echo.

:: 检查 Flutter 是否安装
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 Flutter。请确保 Flutter 已安装并添加到 PATH。
    pause
    exit /b 1
)

echo 1. 检查 Flutter 环境...
flutter doctor --android-licenses >nul 2>&1

echo 2. 获取依赖包...
flutter pub get
if %errorlevel% neq 0 (
    echo 错误: 获取依赖包失败。
    pause
    exit /b 1
)

echo 3. 运行测试...
flutter test
if %errorlevel% neq 0 (
    echo 警告: 测试失败，但继续构建...
)

echo 4. 清理之前的构建...
if exist "build\windows\runner\Release" (
    rmdir /s /q "build\windows\runner\Release"
)

echo 5. 构建 Windows 应用...
flutter build windows --release
if %errorlevel% neq 0 (
    echo 错误: 构建失败。
    pause
    exit /b 1
)

echo 6. 创建分发目录...
if not exist "dist" mkdir dist
if exist "dist\installer" rmdir /s /q "dist\installer"
mkdir "dist\installer"

echo 7. 复制构建文件...
xcopy "build\windows\runner\Release\*" "dist\installer\" /E /I /Y
if %errorlevel% neq 0 (
    echo 错误: 复制文件失败。
    pause
    exit /b 1
)

echo 8. 复制应用文件...
if exist "app_files" (
    xcopy "app_files\*" "dist\installer\app_files\" /E /I /Y
)

echo 9. 复制资源文件...
if exist "assets" (
    xcopy "assets\*" "dist\installer\assets\" /E /I /Y
)

echo 10. 创建安装器信息文件...
echo [Installer Info] > "dist\installer\installer_info.ini"
echo Version=1.0.0 >> "dist\installer\installer_info.ini"
echo BuildDate=%date% %time% >> "dist\installer\installer_info.ini"
echo BuildMachine=%COMPUTERNAME% >> "dist\installer\installer_info.ini"

echo 11. 生成校验和...
if exist "dist\installer\installer_app.exe" (
    certutil -hashfile "dist\installer\installer_app.exe" SHA256 > "dist\installer\checksum.txt"
)

echo.
echo ========================================
echo 构建完成！
echo ========================================
echo.
echo 安装器文件位置: dist\installer\installer_app.exe
echo.

:: 询问是否运行安装器
set /p run_installer="是否要运行安装器进行测试？(y/n): "
if /i "%run_installer%"=="y" (
    echo 启动安装器...
    start "" "dist\installer\installer_app.exe"
)

:: 询问是否打开分发目录
set /p open_dist="是否要打开分发目录？(y/n): "
if /i "%open_dist%"=="y" (
    explorer "dist\installer"
)

echo.
echo 构建脚本执行完成。
pause
