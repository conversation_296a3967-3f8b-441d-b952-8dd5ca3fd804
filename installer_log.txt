[2025-07-22 11:57:56.881255] PowerShell 脚本:
$WshShell = New-Object -comObject WScript.Shell
$Shortcut = $WshShell.CreateShortcut("C:\Users\<USER>\Desktop\PrismBrowse.lnk")
$Shortcut.TargetPath = "C:\Program Files\testt\frontend_re.exe"
$Shortcut.WorkingDirectory = "C:\Program Files\testt"

$Shortcut.Description = "PrismBrowse"
$Shortcut.IconLocation = "C:\Program Files\testt\frontend_re.exe"
$Shortcut.Save()

[2025-07-22 11:57:56.887259] stdout:

[2025-07-22 11:57:56.899774] stderr:

-----------------------------
[2025-07-22 11:57:57.200432] PowerShell 脚本:
$WshShell = New-Object -comObject WScript.Shell
$Shortcut = $WshShell.CreateShortcut("C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\PrismBrowse\PrismBrowse.lnk")
$Shortcut.TargetPath = "C:\Program Files\testt\frontend_re.exe"
$Shortcut.WorkingDirectory = "C:\Program Files\testt"

$Shortcut.Description = "PrismBrowse"
$Shortcut.IconLocation = "C:\Program Files\testt\frontend_re.exe"
$Shortcut.Save()

[2025-07-22 11:57:57.212933] stdout:

[2025-07-22 11:57:57.227932] stderr:

-----------------------------
[2025-07-22 11:57:57.644639] PowerShell 脚本:
$WshShell = New-Object -comObject WScript.Shell
$Shortcut = $WshShell.CreateShortcut("C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\PrismBrowse\卸载 PrismBrowse.lnk")
$Shortcut.TargetPath = "powershell.exe"

$Shortcut.Arguments = "-Command "Remove-Item -Path 'C:\Program Files\testt' -Recurse -Force""
$Shortcut.Description = "卸载 PrismBrowse"

$Shortcut.Save()

[2025-07-22 11:57:57.659149] stdout:

[2025-07-22 11:57:57.671150] stderr:

-----------------------------
[2025-07-22 12:10:35.082129] isRunningAsAdmin: exitCode=2
[2025-07-22 12:10:35.088639] USERPROFILE: 'C:\Users\<USER>\Users\admin\AppData\Roaming'
[2025-07-22 12:10:35.120158] USERNAME: 'admin'
-----------------------------
[2025-07-22 12:10:38.067278] isRunningAsAdmin: exitCode=2
[2025-07-22 12:10:38.081278] USERPROFILE: 'C:\Users\<USER>\Users\admin\AppData\Roaming'
[2025-07-22 12:10:38.103796] USERNAME: 'admin'
-----------------------------
[2025-07-22 12:10:40.575759] isRunningAsAdmin: exitCode=0
[2025-07-22 12:10:40.594268] USERPROFILE: 'C:\Users\<USER>\Users\admin\AppData\Roaming'
[2025-07-22 12:10:40.617782] USERNAME: 'admin'
-----------------------------
[2025-07-22 12:11:15.538144] PowerShell 脚本:
$WshShell = New-Object -comObject WScript.Shell
$Shortcut = $WshShell.CreateShortcut("C:\Users\<USER>\Desktop\PrismBrowse.lnk")
$Shortcut.TargetPath = "C:\Program Files\test\frontend_re.exe"
$Shortcut.WorkingDirectory = "C:\Program Files\test"

$Shortcut.Description = "PrismBrowse"
$Shortcut.IconLocation = "C:\Program Files\test\frontend_re.exe"
$Shortcut.Save()

[2025-07-22 12:11:15.560661] stdout:

[2025-07-22 12:11:15.576659] stderr:

-----------------------------
[2025-07-22 12:11:15.888832] PowerShell 脚本:
$WshShell = New-Object -comObject WScript.Shell
$Shortcut = $WshShell.CreateShortcut("C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\PrismBrowse\PrismBrowse.lnk")
$Shortcut.TargetPath = "C:\Program Files\test\frontend_re.exe"
$Shortcut.WorkingDirectory = "C:\Program Files\test"

$Shortcut.Description = "PrismBrowse"
$Shortcut.IconLocation = "C:\Program Files\test\frontend_re.exe"
$Shortcut.Save()

[2025-07-22 12:11:15.903342] stdout:

[2025-07-22 12:11:15.916342] stderr:

-----------------------------
[2025-07-22 12:11:16.226515] PowerShell 脚本:
$WshShell = New-Object -comObject WScript.Shell
$Shortcut = $WshShell.CreateShortcut("C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\PrismBrowse\卸载 PrismBrowse.lnk")
$Shortcut.TargetPath = "powershell.exe"

$Shortcut.Arguments = "-Command "Remove-Item -Path 'C:\Program Files\test' -Recurse -Force""
$Shortcut.Description = "卸载 PrismBrowse"

$Shortcut.Save()

[2025-07-22 12:11:16.243020] stdout:

[2025-07-22 12:11:16.257028] stderr:

-----------------------------
