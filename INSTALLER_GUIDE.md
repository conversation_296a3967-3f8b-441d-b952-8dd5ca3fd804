# Flutter Windows 安装器使用指南

这是一个完整的 Flutter Windows 应用安装器，类似于 Inno Setup，但使用自定义的 Flutter UI。

## 功能特性

### 🎨 用户界面
- 现代化的 Material Design 界面
- 中文本地化支持
- 响应式布局设计
- 流畅的动画效果
- 步骤指示器

### 📦 安装功能
- 自定义安装路径选择
- 磁盘空间检查
- 文件复制进度显示
- 快捷方式创建（桌面、开始菜单）
- 系统注册表注册
- 权限检查和管理

### 🛠️ 高级功能
- 许可协议显示和确认
- 错误处理和恢复建议
- 安装完成后自动启动应用
- 卸载功能支持
- 备份和恢复机制

## 项目结构

```
installer_app/
├── lib/
│   ├── models/                 # 数据模型
│   │   ├── installer_config.dart
│   │   └── uninstaller_config.dart
│   ├── providers/              # 状态管理
│   │   └── installer_provider.dart
│   ├── screens/                # 主要界面
│   │   └── installer_screen.dart
│   ├── services/               # 业务逻辑
│   │   ├── file_service.dart
│   │   ├── system_service.dart
│   │   └── uninstaller_service.dart
│   ├── widgets/                # UI 组件
│   │   ├── welcome_step.dart
│   │   ├── license_step.dart
│   │   ├── path_selection_step.dart
│   │   ├── installation_step.dart
│   │   ├── completion_step.dart
│   │   └── error_step.dart
│   └── main.dart
├── app_files/                  # 要安装的应用文件
│   ├── MyApp.exe
│   ├── config.ini
│   ├── README.txt
│   └── LICENSE
├── assets/                     # 资源文件
│   ├── license.txt
│   └── images/
└── windows/                    # Windows 平台配置
```

## 使用方法

### 1. 准备应用文件

将你要安装的 Flutter 应用文件放在 `app_files/` 目录中：

```bash
app_files/
├── YourApp.exe              # 主可执行文件
├── data/                    # 数据文件夹
├── flutter_windows.dll      # Flutter 运行时
├── msvcp140.dll            # Visual C++ 运行时
├── vcruntime140.dll        # Visual C++ 运行时
└── vcruntime140_1.dll      # Visual C++ 运行时
```

### 2. 配置安装器

在 `lib/providers/installer_provider.dart` 中修改应用信息：

```dart
void _initializeConfig() {
  _config = InstallerConfig(
    appName: '你的应用名称',
    version: '1.0.0',
    publisher: '你的公司名称',
    installPath: r'C:\Program Files\YourApp',
    createDesktopShortcut: true,
    createStartMenuShortcut: true,
    addToPath: false,
  );
}
```

### 3. 自定义许可协议

修改 `assets/license.txt` 文件内容，或在 `InstallerConfig` 中设置 `licenseText` 属性。

### 4. 构建安装器

```bash
# 构建 Windows 应用
flutter build windows --release

# 或者构建 MSIX 包
flutter pub run msix:create
```

### 5. 分发安装器

将构建好的安装器分发给用户，用户运行后会看到以下安装步骤：

1. **欢迎页面** - 显示应用信息和安装说明
2. **许可协议** - 用户必须同意才能继续
3. **路径选择** - 选择安装目录和快捷方式选项
4. **安装进度** - 显示文件复制和配置进度
5. **安装完成** - 显示安装结果和启动选项

## 自定义配置

### 修改应用图标

1. 替换 `assets/app_icon.ico` 文件
2. 在 `pubspec.yaml` 中更新图标路径

### 添加自定义安装步骤

1. 在 `lib/providers/installer_provider.dart` 中添加新的 `InstallerStep`
2. 在 `lib/screens/installer_screen.dart` 中添加对应的 UI
3. 创建新的 widget 文件处理该步骤的逻辑

### 修改安装逻辑

在 `lib/services/file_service.dart` 中修改 `copyApplicationFiles` 方法：

```dart
Future<void> copyApplicationFiles(String installPath, {
  Function(double progress, String currentFile)? onProgress,
}) async {
  // 从网络下载
  await downloadAndExtract(installPath, 'https://your-server.com/app.zip', onProgress: onProgress);
  
  // 或从本地复制
  await _copyFromLocalPath(installPath, onProgress: onProgress);
}
```

## 高级功能

### 权限管理

安装器会自动检查和请求管理员权限：

```dart
final systemService = SystemService();
if (!systemService.isRunningAsAdmin()) {
  await systemService.requestAdminPrivileges();
}
```

### 卸载功能

安装器会自动注册卸载信息到 Windows 注册表，用户可以通过"程序和功能"卸载应用。

### 错误处理

安装器包含完整的错误处理机制：
- 磁盘空间不足
- 权限不够
- 文件损坏
- 网络连接问题

### 多语言支持

可以轻松添加多语言支持：

1. 使用 `flutter_localizations` 包
2. 创建 ARB 文件定义翻译
3. 在 UI 中使用 `Localizations.of(context)`

## 部署建议

### 代码签名

为了避免 Windows Defender 警告，建议对安装器进行代码签名：

```bash
signtool sign /f certificate.pfx /p password /t http://timestamp.digicert.com installer.exe
```

### 分发方式

1. **直接下载** - 提供 EXE 文件下载
2. **MSIX 包** - 通过 Microsoft Store 或侧载安装
3. **MSI 包** - 使用 WiX Toolset 创建 MSI 安装包

### 自动更新

可以集成自动更新功能：

1. 检查服务器上的版本信息
2. 下载更新包
3. 应用更新并重启应用

## 故障排除

### 常见问题

1. **权限不足** - 以管理员身份运行安装器
2. **磁盘空间不足** - 清理磁盘空间或选择其他安装位置
3. **杀毒软件拦截** - 将安装器添加到白名单
4. **依赖缺失** - 安装 Visual C++ Redistributable

### 调试模式

在开发时可以启用调试模式：

```dart
const bool kDebugMode = true; // 在 main.dart 中设置

if (kDebugMode) {
  print('Debug: Installation step - ${provider.currentStep}');
}
```

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个安装器！

## 许可证

MIT License - 详见 LICENSE 文件
