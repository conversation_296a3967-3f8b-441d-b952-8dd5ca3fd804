import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/installer_provider.dart';

class InstallationStep extends StatefulWidget {
  const InstallationStep({super.key});

  @override
  State<InstallationStep> createState() => _InstallationStepState();
}

class _InstallationStepState extends State<InstallationStep>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<InstallerProvider>(
      builder: (context, provider, child) {
        return AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: ScaleTransition(
                scale: _scaleAnimation,
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // 安装图标和标题
                      Icon(
                        Icons.download,
                        size: 56,
                        color: Colors.blue.shade600,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        '正在安装...'
                        ,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade700,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        provider.statusMessage,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey.shade600,
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),
                      // 进度条
                      LinearProgressIndicator(
                        value: provider.progress,
                        minHeight: 8,
                        backgroundColor: Colors.grey.shade200,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Colors.blue.shade600,
                        ),
                      ),
                      const SizedBox(height: 12),
                      // 步骤指示器
                      _buildStepIndicator(provider),
                      const SizedBox(height: 18),
                      // 简要提示
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.amber.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.amber.shade100),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: Colors.amber.shade700,
                              size: 18,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                '请勿关闭窗口',
                                style: TextStyle(
                                  color: Colors.amber.shade700,
                                  fontSize: 13,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildInstallationDetails(InstallerProvider provider) {
    return Column(
      children: [
        _buildDetailRow(
          '应用程序',
          provider.config.appName,
          Icons.apps,
        ),
        _buildDetailRow(
          '安装位置',
          provider.config.installPath,
          Icons.folder,
        ),
        _buildDetailRow(
          '版本',
          provider.config.version,
          Icons.info,
        ),
        if (provider.config.createDesktopShortcut)
          _buildDetailRow(
            '桌面快捷方式',
            '将创建',
            Icons.desktop_windows,
          ),
        if (provider.config.createStartMenuShortcut)
          _buildDetailRow(
            '开始菜单',
            '将创建',
            Icons.menu,
          ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: Colors.grey.shade600,
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepIndicator(InstallerProvider provider) {
    final steps = [
      {'label': '准备', 'icon': Icons.settings, 'threshold': 0.1},
      {'label': '复制文件', 'icon': Icons.file_copy, 'threshold': 0.6},
      {'label': '创建快捷方式', 'icon': Icons.link, 'threshold': 0.8},
      {'label': '注册应用', 'icon': Icons.app_registration, 'threshold': 0.9},
      {'label': '完成', 'icon': Icons.check_circle, 'threshold': 1.0},
    ];

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: steps.map((step) {
        final isActive = provider.progress >= (step['threshold'] as double);
        final isCurrent = provider.progress < (step['threshold'] as double) &&
            (steps.indexOf(step) == 0 || 
             provider.progress >= (steps[steps.indexOf(step) - 1]['threshold'] as double));

        return Column(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isActive 
                    ? Colors.green.shade100 
                    : isCurrent 
                        ? Colors.blue.shade100 
                        : Colors.grey.shade100,
                shape: BoxShape.circle,
                border: Border.all(
                  color: isActive 
                      ? Colors.green.shade400 
                      : isCurrent 
                          ? Colors.blue.shade400 
                          : Colors.grey.shade300,
                  width: 2,
                ),
              ),
              child: Icon(
                step['icon'] as IconData,
                size: 16,
                color: isActive 
                    ? Colors.green.shade600 
                    : isCurrent 
                        ? Colors.blue.shade600 
                        : Colors.grey.shade400,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              step['label'] as String,
              style: TextStyle(
                fontSize: 10,
                color: isActive 
                    ? Colors.green.shade600 
                    : isCurrent 
                        ? Colors.blue.shade600 
                        : Colors.grey.shade400,
                fontWeight: isActive || isCurrent ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        );
      }).toList(),
    );
  }
}
