@echo off
echo ========================================
echo 测试权限提升功能
echo ========================================
echo.

echo 构建应用...
flutter build windows --release

if %errorlevel% neq 0 (
    echo 构建失败！
    pause
    exit /b 1
)

echo.
echo 测试权限流程...
echo.

echo 当前用户权限状态:
net session >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 当前以管理员身份运行
    echo.
    echo 测试场景: 应用将直接显示"管理员权限已获取"并进入安装界面
) else (
    echo ✗ 当前以普通用户身份运行
    echo.
    echo 测试场景: 应用将显示权限请求界面
    echo.
    echo 测试步骤:
    echo 1. 应用启动后会显示权限检查界面
    echo 2. 点击"获取管理员权限"会显示UAC风格对话框
    echo 3. 点击"是"会尝试以管理员身份重启应用
    echo 4. 点击"以普通用户继续"会直接进入安装界面
)

echo.
echo 启动应用进行测试...
start "" "build\windows\runner\Release\installer_app.exe"

echo.
echo 请测试以下功能:
echo.
echo ✓ 权限检查界面是否正确显示
echo ✓ UAC风格对话框是否美观
echo ✓ "获取管理员权限"按钮是否正常工作
echo ✓ "以普通用户继续"按钮是否正常工作
echo ✓ 权限获取后是否正确进入安装界面
echo ✓ 在安装界面选择Program Files目录是否正常
echo.

echo 注意事项:
echo - 如果点击"获取管理员权限"，当前窗口会关闭并以管理员身份重新打开
echo - 管理员权限下可以安装到任何目录
echo - 普通用户权限下建议安装到用户目录
echo.

pause
